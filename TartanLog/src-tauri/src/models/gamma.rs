// Gamma数据模型
// 对应C#中的Gamma相关数据结构

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Gamma数据点
/// 对应C#中的GammaDataPoint结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GammaDataPoint {
    pub id: i64,
    pub depth: f64,
    pub gamma_value: f64,
    pub timestamp: DateTime<Utc>,
    pub bit_run_id: Option<i64>,
    pub survey_point_id: Option<i64>,
}

/// Gamma数据集
/// 对应C#中的MezDataSet结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GammaDataSet {
    pub bit_run_id: i64,
    pub start_depth: f64,
    pub end_depth: f64,
    pub data_points: Vec<GammaDataPoint>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 实时Gamma数据
/// 对应C#中的实时数据传输结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RealTimeGammaData {
    pub current_depth: f64,
    pub current_gamma: f64,
    pub timestamp: DateTime<Utc>,
    pub bit_run_id: i64,
    pub is_drilling: bool,
}

/// Gamma数据查询参数
/// 用于数据库查询的参数结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GammaQueryParams {
    pub bit_run_id: Option<i64>,
    pub start_depth: Option<f64>,
    pub end_depth: Option<f64>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub limit: Option<i32>,
}

impl Default for GammaQueryParams {
    fn default() -> Self {
        Self {
            bit_run_id: None,
            start_depth: None,
            end_depth: None,
            start_time: None,
            end_time: None,
            limit: Some(1000), // 默认限制1000条记录
        }
    }
}

/// Gamma数据统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GammaStatistics {
    pub total_points: i64,
    pub min_gamma: f64,
    pub max_gamma: f64,
    pub avg_gamma: f64,
    pub depth_range: (f64, f64),
    pub time_range: (DateTime<Utc>, DateTime<Utc>),
}
